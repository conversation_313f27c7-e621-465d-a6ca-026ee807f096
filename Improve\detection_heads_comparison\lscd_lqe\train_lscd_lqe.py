"""
Detect_LSCD_LQE 检测头测试脚本

基于 innovation1-c2f-emscp-aaaf 的成功配置
检测头改进: 轻量化 + 质量评估
"""

import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 获取脚本所在目录并切换
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# 添加项目根目录到路径
sys.path.append('../../../')

from ultralytics import YOLO
import torch

def get_fire_smoke_specialized_augmentation():
    """火焰烟雾专用数据增强参数 (基于成功配置)"""
    return {
        'hsv_h': 0.018, 'hsv_s': 0.75, 'hsv_v': 0.45,
        'degrees': 12.0, 'translate': 0.12, 'scale': 0.7, 'shear': 4.0,
        'perspective': 0.0005, 'flipud': 0.0, 'fliplr': 0.35,
        'mosaic': 0.75, 'mixup': 0.12, 'copy_paste': 0.08,
        'erasing': 0.3, 'auto_augment': 'randaugment',
        'bgr': 0.0, 'crop_fraction': 1.0,
    }

def get_fire_smoke_optimizer_config():
    """火焰烟雾检测优化的训练参数"""
    return {
        'lr0': 0.0015, 'lrf': 0.00015, 'momentum': 0.937, 'weight_decay': 0.0008,
        'warmup_epochs': 4.0, 'warmup_momentum': 0.8, 'warmup_bias_lr': 0.1,
        'cos_lr': True,
    }

def get_fire_smoke_loss_weights():
    """火焰烟雾检测专用损失权重"""
    return {'box': 8.2, 'cls': 0.7, 'dfl': 1.3}

def get_fire_smoke_training_strategy():
    """火焰烟雾检测训练策略"""
    return {
        'close_mosaic': 15, 'patience': 20, 'save_period': 5,
        'val': True, 'plots': True, 'cache': True, 'amp': True,
        'single_cls': False, 'rect': False, 'deterministic': True, 'seed': 42,
    }

def main():
    try:
        print("🔥 Detect_LSCD_LQE 测试")
        print("🎯 检测头改进: 轻量化 + 质量评估")

        if not torch.cuda.is_available():
            print("❌ CUDA不可用，请检查GPU环境")
            return None

        print(f"✅ GPU: {torch.cuda.get_device_name(0)}")

        config_path = os.path.join(script_dir, 'config_lscd_lqe.yaml')
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        model = YOLO(config_path)

        aug_params = get_fire_smoke_specialized_augmentation()
        opt_params = get_fire_smoke_optimizer_config()
        loss_params = get_fire_smoke_loss_weights()
        strategy_params = get_fire_smoke_training_strategy()

        results = model.train(
            data='../../../ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
            epochs=20, batch=-1, imgsz=640, device=0, workers=8,
            project='../results', name='lscd_lqe_test', exist_ok=True,
            optimizer="AdamW", verbose=True, pretrained=True,
            **aug_params, **opt_params, **loss_params, **strategy_params,
        )

        print("\n🎉 Detect_LSCD_LQE 测试完成！")
        print("📁 结果保存在: ../results/lscd_lqe_test")
        return results

    except Exception as e:
        print(f"❌ Detect_LSCD_LQE 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
