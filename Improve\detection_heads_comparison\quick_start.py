"""
YOLOv8 检测头改进方案快速启动脚本

提供便捷的测试和分析功能
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🔬 YOLOv8 检测头改进方案统一测试环境")
    print("=" * 60)
    print("🎯 目标: 统一测试和比较各种检测头改进方案")
    print("🚀 功能: 快速验证、性能对比、最优选择")
    print("=" * 60)

def show_menu():
    """显示菜单"""
    print("\n📋 可用操作:")
    print("1. 🔬 运行所有检测头测试")
    print("2. 📊 分析测试结果")
    print("3. 🎯 运行单个检测头测试")
    print("4. 📁 查看项目结构")
    print("5. 🔧 环境检查")
    print("6. 📖 查看帮助文档")
    print("0. 🚪 退出")
    print("-" * 40)

def check_environment():
    """检查环境"""
    print("🔧 环境检查...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA: {torch.version.cuda}")
            print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
            print(f"✅ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        else:
            print("⚠️ CUDA不可用，将使用CPU训练（速度较慢）")
    except ImportError:
        print("❌ PyTorch未安装")
        return False
    
    try:
        from ultralytics import YOLO
        print("✅ Ultralytics YOLO已安装")
    except ImportError:
        print("❌ Ultralytics未安装，请运行: pip install ultralytics")
        return False
    
    # 检查数据集
    dataset_path = Path("../../../ultralytics/cfg/datasets/fire-smoke-dataset.yaml")
    if dataset_path.exists():
        print("✅ 数据集配置文件存在")
    else:
        print("⚠️ 数据集配置文件不存在，请检查路径")
    
    print("✅ 环境检查完成")
    return True

def show_project_structure():
    """显示项目结构"""
    print("📁 项目结构:")
    print("""
detection_heads_comparison/
├── README.md                    # 项目说明文档
├── quick_start.py              # 快速启动脚本 (当前)
├── run_all_tests.py            # 批量测试脚本
├── compare_results.py          # 结果对比分析脚本
├── dyhead/                     # 动态头测试
│   ├── config_dyhead.yaml      # 配置文件
│   └── train_dyhead.py         # 训练脚本
├── taddh/                      # 任务感知动态检测头测试
├── lscd/                       # 轻量化共享卷积检测头测试
├── efficient/                  # 高效检测头测试
├── seam/                       # 空间-通道增强注意力检测头测试
├── multiseam/                  # 多尺度SEAM检测头测试
├── lqe/                        # 位置质量评估检测头测试
├── ladh/                       # 位置感知检测头测试
├── lscd_lqe/                   # 组合检测头测试
└── results/                    # 统一结果存储
    └── test_session_YYYYMMDD_HHMMSS/
        ├── *_result.json       # 各检测头测试结果
        ├── test_summary_report.md
        ├── detailed_comparison_report.md
        ├── comparison_charts.png
        └── performance_metrics.csv
""")

def run_all_tests():
    """运行所有测试"""
    print("🔬 开始运行所有检测头测试...")
    print("⏱️ 预计耗时: 30-60分钟 (取决于GPU性能)")
    
    confirm = input("确认开始测试? (y/N): ").lower().strip()
    if confirm != 'y':
        print("❌ 测试已取消")
        return
    
    try:
        result = subprocess.run([sys.executable, 'run_all_tests.py'], 
                              capture_output=False, text=True)
        if result.returncode == 0:
            print("✅ 所有测试完成")
        else:
            print("❌ 测试过程中出现错误")
    except Exception as e:
        print(f"❌ 运行测试失败: {e}")

def analyze_results():
    """分析结果"""
    print("📊 开始分析测试结果...")
    
    try:
        result = subprocess.run([sys.executable, 'compare_results.py'], 
                              capture_output=False, text=True)
        if result.returncode == 0:
            print("✅ 结果分析完成")
        else:
            print("❌ 分析过程中出现错误")
    except Exception as e:
        print(f"❌ 分析结果失败: {e}")

def run_single_test():
    """运行单个测试"""
    print("🎯 选择要测试的检测头:")

    heads = {
        '1': ('dyhead', 'DyHead (动态头)'),
        '2': ('taddh', 'TADDH (任务感知动态检测头)'),
        '3': ('lscd', 'LSCD (轻量化共享卷积检测头)'),
        '4': ('efficient', 'Efficient Head (高效检测头)'),
        '5': ('seam', 'SEAM Head (空间-通道增强注意力检测头)'),
        '6': ('multiseam', 'MultiSEAM Head (多尺度SEAM检测头)'),
        '7': ('lqe', 'LQE Head (位置质量评估检测头)'),
        '8': ('ladh', 'LADH (位置感知检测头)'),
        '9': ('lscd_lqe', 'LSCD-LQE (组合检测头)'),
    }

    for key, (folder, name) in heads.items():
        print(f"{key}. {name}")

    choice = input("请选择 (1-9): ").strip()

    if choice in heads:
        folder, name = heads[choice]
        print(f"🚀 开始测试: {name}")

        # 获取脚本所在目录
        script_dir = Path(__file__).parent
        head_dir = script_dir / folder
        train_script = head_dir / f'train_{folder}.py'

        print(f"📁 检测头目录: {head_dir}")
        print(f"📄 训练脚本: {train_script}")

        if train_script.exists():
            try:
                print(f"🔄 正在运行: {train_script}")
                result = subprocess.run([sys.executable, str(train_script)],
                                      cwd=str(head_dir), capture_output=False, text=True)
                if result.returncode == 0:
                    print(f"✅ {name} 测试完成")
                else:
                    print(f"❌ {name} 测试失败，返回码: {result.returncode}")
            except Exception as e:
                print(f"❌ 运行测试失败: {e}")
        else:
            print(f"❌ 训练脚本不存在: {train_script}")
            print(f"📁 目录内容: {list(head_dir.iterdir()) if head_dir.exists() else '目录不存在'}")
    else:
        print("❌ 无效选择")

def show_help():
    """显示帮助"""
    print("📖 帮助文档:")
    print("""
🎯 使用说明:

1. 环境准备:
   - 确保安装了 PyTorch 和 Ultralytics
   - 确保有可用的GPU (推荐)
   - 确保数据集配置正确

2. 快速开始:
   - 选择 "5. 环境检查" 确认环境正常
   - 选择 "1. 运行所有检测头测试" 开始批量测试
   - 选择 "2. 分析测试结果" 查看对比报告

3. 单独测试:
   - 选择 "3. 运行单个检测头测试" 测试特定检测头
   - 适合调试和快速验证

4. 结果查看:
   - 测试结果保存在 results/ 目录下
   - 包含详细报告、图表和原始数据

🔧 故障排除:

1. 模块导入错误:
   - 确保所有检测头模块已正确导入
   - 检查 ultralytics.nn.extra_modules.head

2. GPU内存不足:
   - 减少批次大小
   - 使用更小的模型

3. 数据集路径错误:
   - 检查 fire-smoke-dataset.yaml 路径
   - 确认数据集文件存在

📞 技术支持:
   - 查看 README.md 获取详细信息
   - 检查日志文件排查问题
""")

def main():
    """主函数"""
    print_banner()
    
    while True:
        show_menu()
        choice = input("请选择操作 (0-6): ").strip()
        
        if choice == '0':
            print("👋 再见！")
            break
        elif choice == '1':
            run_all_tests()
        elif choice == '2':
            analyze_results()
        elif choice == '3':
            run_single_test()
        elif choice == '4':
            show_project_structure()
        elif choice == '5':
            check_environment()
        elif choice == '6':
            show_help()
        else:
            print("❌ 无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
