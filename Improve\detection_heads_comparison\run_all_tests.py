"""
YOLOv8 检测头改进方案批量测试脚本

统一测试所有检测头改进方案，生成对比报告
基于 innovation1-c2f-emscp-aaaf 的成功配置
"""

import os
import sys
import time
import json
import warnings
import subprocess
from datetime import datetime
from pathlib import Path

warnings.filterwarnings('ignore')

# 添加项目根目录到路径
script_dir = Path(__file__).parent
project_root = script_dir.parent.parent
sys.path.append(str(project_root))

def get_detection_heads_config():
    """获取所有检测头配置"""
    return {
        'dyhead': {
            'name': 'DyHead (动态头)',
            'detect_class': 'Detect_DyHead',
            'config_params': '[nc, 128, 1]',
            'description': '三种注意力机制 + 可变形卷积',
            'expected_improvement': '多尺度目标检测精度提升'
        },
        'taddh': {
            'name': 'TADDH (任务感知动态检测头)',
            'detect_class': 'Detect_TADDH',
            'config_params': '[nc, 512]',
            'description': '任务分解 + 动态对齐',
            'expected_improvement': '减少分类回归冲突'
        },
        'lscd': {
            'name': 'LSCD (轻量化共享卷积检测头)',
            'detect_class': 'Detect_LSCD',
            'config_params': '[nc, 256]',
            'description': '共享卷积 + 轻量化设计',
            'expected_improvement': '降低复杂度，提高推理速度'
        },
        'efficient': {
            'name': 'Efficient Head (高效检测头)',
            'detect_class': 'Detect_Efficient',
            'config_params': '[nc]',
            'description': '高效架构 + 参数优化',
            'expected_improvement': '平衡精度和效率'
        },
        'seam': {
            'name': 'SEAM Head (空间-通道增强注意力检测头)',
            'detect_class': 'Detect_SEAM',
            'config_params': '[nc]',
            'description': '空间-通道注意力机制',
            'expected_improvement': '增强特征表达，提高小目标检测'
        },
        'multiseam': {
            'name': 'MultiSEAM Head (多尺度SEAM检测头)',
            'detect_class': 'Detect_MultiSEAM',
            'config_params': '[nc]',
            'description': '多尺度注意力 + 特征融合',
            'expected_improvement': '更好的多尺度目标检测'
        },
        'lqe': {
            'name': 'LQE Head (位置质量评估检测头)',
            'detect_class': 'Detect_LQE',
            'config_params': '[nc]',
            'description': '位置质量评估 + 分布统计',
            'expected_improvement': '提高边界框定位精度'
        },
        'ladh': {
            'name': 'LADH (位置感知检测头)',
            'detect_class': 'Detect_LADH',
            'config_params': '[nc]',
            'description': '位置感知 + 动态蛇形卷积',
            'expected_improvement': '不规则目标检测，提高定位精度'
        },
        'lscd_lqe': {
            'name': 'LSCD-LQE (组合检测头)',
            'detect_class': 'Detect_LSCD_LQE',
            'config_params': '[nc, 256]',
            'description': '轻量化 + 质量评估',
            'expected_improvement': '综合性能提升，适合实际部署'
        }
    }

def create_results_directory():
    """创建结果存储目录"""
    results_dir = script_dir / 'results'
    results_dir.mkdir(exist_ok=True)

    # 创建时间戳子目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    test_session_dir = results_dir / f'test_session_{timestamp}'
    test_session_dir.mkdir(exist_ok=True)

    return test_session_dir

def create_missing_configs():
    """创建缺失的配置文件和训练脚本"""

    # 需要创建的剩余检测头配置
    remaining_heads = {
        'multiseam': {
            'detect_class': 'Detect_MultiSEAM',
            'config_params': '[nc]',
            'description': '多尺度注意力 + 特征融合'
        },
        'lqe': {
            'detect_class': 'Detect_LQE',
            'config_params': '[nc]',
            'description': '位置质量评估 + 分布统计'
        },
        'ladh': {
            'detect_class': 'Detect_LADH',
            'config_params': '[nc]',
            'description': '位置感知 + 动态蛇形卷积'
        },
        'lscd_lqe': {
            'detect_class': 'Detect_LSCD_LQE',
            'config_params': '[nc, 256]',
            'description': '轻量化 + 质量评估'
        }
    }

    for head_name, head_info in remaining_heads.items():
        head_dir = script_dir / head_name
        config_file = head_dir / f'config_{head_name}.yaml'
        train_file = head_dir / f'train_{head_name}.py'

        # 创建配置文件
        if not config_file.exists():
            create_config_file(head_name, head_info, config_file)

        # 创建训练脚本
        if not train_file.exists():
            create_train_script(head_name, head_info, train_file)

def create_config_file(head_name, head_info, config_file):
    """创建配置文件"""
    config_content = f"""# YOLOv8 + {head_info['detect_class']} 配置文件
# 基于 innovation1-c2f-emscp-aaaf 成功配置
# 检测头改进: {head_info['description']}

# Parameters
nc: 2  # number of classes (fire, smoke)
scales: # model compound scaling constants
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]  # YOLOv8n summary: 225 layers,  3157200 parameters,  3157184 gradients,   8.9 GFLOPs

# YOLOv8.0n backbone (保持标准backbone)
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]]  # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]]  # 1-P2/4
  - [-1, 3, C2f, [128, True]]
  - [-1, 1, Conv, [256, 3, 2]]  # 3-P3/8
  - [-1, 6, C2f, [256, True]]
  - [-1, 1, Conv, [512, 3, 2]]  # 5-P4/16
  - [-1, 6, C2f, [512, True]]
  - [-1, 1, Conv, [1024, 3, 2]]  # 7-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [-1, 1, SPPF, [1024, 5]]  # 9

# YOLOv8.0n head with {head_info['detect_class']}
head:
  - [-1, 1, nn.Upsample, [None, 2, 'nearest']]
  - [[-1, 6], 1, Concat, [1]]  # cat backbone P4
  - [-1, 3, C2f, [512]]  # 12

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']]
  - [[-1, 4], 1, Concat, [1]]  # cat backbone P3
  - [-1, 3, C2f, [256]]  # 15 (P3/8-small)

  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 12], 1, Concat, [1]]  # cat head P4
  - [-1, 3, C2f, [512]]  # 18 (P4/16-medium)

  - [-1, 1, Conv, [512, 3, 2]]
  - [[-1, 9], 1, Concat, [1]]  # cat head P5
  - [-1, 3, C2f, [1024]]  # 21 (P5/32-large)

  - [[15, 18, 21], 1, {head_info['detect_class']}, {head_info['config_params']}]  # {head_info['detect_class']}检测头
"""

    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)

def run_single_test(head_name, head_config, results_dir):
    """运行单个检测头测试"""
    print(f"\n🚀 开始测试: {head_config['name']}")
    print(f"📝 描述: {head_config['description']}")
    print(f"🎯 预期改进: {head_config['expected_improvement']}")
    
    head_dir = script_dir / head_name
    train_script = head_dir / f'train_{head_name}.py'
    
    if not train_script.exists():
        print(f"❌ 训练脚本不存在: {train_script}")
        return None
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 运行训练脚本
        result = subprocess.run(
            [sys.executable, str(train_script)],
            cwd=str(head_dir),
            capture_output=True,
            text=True,
            timeout=3600  # 1小时超时
        )
        
        # 记录结束时间
        end_time = time.time()
        training_time = end_time - start_time
        
        # 保存结果
        test_result = {
            'head_name': head_name,
            'head_config': head_config,
            'training_time': training_time,
            'success': result.returncode == 0,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存到文件
        result_file = results_dir / f'{head_name}_result.json'
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(test_result, f, indent=2, ensure_ascii=False)
        
        if result.returncode == 0:
            print(f"✅ {head_config['name']} 测试完成")
            print(f"⏱️ 训练时间: {training_time:.1f}秒")
        else:
            print(f"❌ {head_config['name']} 测试失败")
            print(f"错误信息: {result.stderr[:200]}...")
        
        return test_result
        
    except subprocess.TimeoutExpired:
        print(f"⏰ {head_config['name']} 测试超时")
        return {
            'head_name': head_name,
            'head_config': head_config,
            'success': False,
            'error': 'Timeout',
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        print(f"❌ {head_config['name']} 测试异常: {e}")
        return {
            'head_name': head_name,
            'head_config': head_config,
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

def generate_summary_report(results, results_dir):
    """生成汇总报告"""
    print("\n📊 生成汇总报告...")
    
    # 统计信息
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r and r.get('success', False))
    failed_tests = total_tests - successful_tests
    
    # 生成Markdown报告
    report_content = f"""# YOLOv8 检测头改进方案测试报告

## 📋 测试概览

- **测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **总测试数**: {total_tests}
- **成功测试**: {successful_tests}
- **失败测试**: {failed_tests}
- **成功率**: {successful_tests/total_tests*100:.1f}%

## 🎯 测试结果详情

| 检测头 | 状态 | 训练时间 | 描述 | 预期改进 |
|--------|------|----------|------|----------|
"""
    
    for result in results:
        if result:
            status = "✅ 成功" if result.get('success', False) else "❌ 失败"
            training_time = f"{result.get('training_time', 0):.1f}s" if result.get('training_time') else "N/A"
            config = result.get('head_config', {})
            
            report_content += f"| {config.get('name', 'Unknown')} | {status} | {training_time} | {config.get('description', 'N/A')} | {config.get('expected_improvement', 'N/A')} |\n"
    
    report_content += f"""
## 📈 性能分析

### 成功的检测头改进方案
"""
    
    successful_heads = [r for r in results if r and r.get('success', False)]
    if successful_heads:
        for result in successful_heads:
            config = result.get('head_config', {})
            report_content += f"""
#### {config.get('name', 'Unknown')}
- **检测类**: `{config.get('detect_class', 'Unknown')}`
- **训练时间**: {result.get('training_time', 0):.1f}秒
- **技术特点**: {config.get('description', 'N/A')}
- **预期优势**: {config.get('expected_improvement', 'N/A')}
"""
    else:
        report_content += "\n暂无成功的测试结果。\n"
    
    report_content += f"""
### 失败的检测头改进方案
"""
    
    failed_heads = [r for r in results if r and not r.get('success', False)]
    if failed_heads:
        for result in failed_heads:
            config = result.get('head_config', {})
            error_info = result.get('error', result.get('stderr', 'Unknown error'))
            report_content += f"""
#### {config.get('name', 'Unknown')}
- **失败原因**: {error_info[:100]}...
- **建议**: 检查模块导入和配置文件
"""
    else:
        report_content += "\n所有测试均成功完成。\n"
    
    report_content += f"""
## 🔧 问题排查

### 常见问题
1. **模块导入错误**: 确保所有检测头模块已正确导入到 `ultralytics.nn.extra_modules.head`
2. **配置文件错误**: 检查YAML配置文件中的检测头类名和参数
3. **GPU内存不足**: 考虑减少批次大小或使用更小的模型
4. **数据集路径**: 确认数据集路径配置正确

### 下一步建议
1. 对成功的检测头进行完整训练（更多epochs）
2. 分析各检测头的详细性能指标
3. 选择最优方案进行部署测试
4. 根据具体任务需求选择合适的检测头

---
**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    # 保存报告
    report_file = results_dir / 'test_summary_report.md'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📄 汇总报告已保存: {report_file}")
    return report_file

def create_train_script(head_name, head_info, train_file):
    """创建训练脚本"""
    train_content = f'''"""
{head_info['detect_class']} 检测头测试脚本

基于 innovation1-c2f-emscp-aaaf 的成功配置
检测头改进: {head_info['description']}
"""

import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 获取脚本所在目录并切换
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# 添加项目根目录到路径
sys.path.append('../../../')

from ultralytics import YOLO
import torch

def get_fire_smoke_specialized_augmentation():
    """火焰烟雾专用数据增强参数 (基于成功配置)"""
    return {{
        'hsv_h': 0.018, 'hsv_s': 0.75, 'hsv_v': 0.45,
        'degrees': 12.0, 'translate': 0.12, 'scale': 0.7, 'shear': 4.0,
        'perspective': 0.0005, 'flipud': 0.0, 'fliplr': 0.35,
        'mosaic': 0.75, 'mixup': 0.12, 'copy_paste': 0.08,
        'erasing': 0.3, 'auto_augment': 'randaugment',
        'bgr': 0.0, 'crop_fraction': 1.0,
    }}

def get_fire_smoke_optimizer_config():
    """火焰烟雾检测优化的训练参数"""
    return {{
        'lr0': 0.0015, 'lrf': 0.00015, 'momentum': 0.937, 'weight_decay': 0.0008,
        'warmup_epochs': 4.0, 'warmup_momentum': 0.8, 'warmup_bias_lr': 0.1,
        'cos_lr': True,
    }}

def get_fire_smoke_loss_weights():
    """火焰烟雾检测专用损失权重"""
    return {{'box': 8.2, 'cls': 0.7, 'dfl': 1.3}}

def get_fire_smoke_training_strategy():
    """火焰烟雾检测训练策略"""
    return {{
        'close_mosaic': 15, 'patience': 20, 'save_period': 5,
        'val': True, 'plots': True, 'cache': True, 'amp': True,
        'single_cls': False, 'rect': False, 'deterministic': True, 'seed': 42,
    }}

def main():
    try:
        print("🔥 {head_info['detect_class']} 测试")
        print("🎯 检测头改进: {head_info['description']}")

        if not torch.cuda.is_available():
            print("❌ CUDA不可用，请检查GPU环境")
            return None

        print(f"✅ GPU: {{torch.cuda.get_device_name(0)}}")

        config_path = os.path.join(script_dir, 'config_{head_name}.yaml')
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {{config_path}}")

        model = YOLO(config_path)

        aug_params = get_fire_smoke_specialized_augmentation()
        opt_params = get_fire_smoke_optimizer_config()
        loss_params = get_fire_smoke_loss_weights()
        strategy_params = get_fire_smoke_training_strategy()

        results = model.train(
            data='../../../ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
            epochs=20, batch=-1, imgsz=640, device=0, workers=8,
            project='../results', name='{head_name}_test', exist_ok=True,
            optimizer="AdamW", verbose=True, pretrained=True,
            **aug_params, **opt_params, **loss_params, **strategy_params,
        )

        print("\\n🎉 {head_info['detect_class']} 测试完成！")
        print("📁 结果保存在: ../results/{head_name}_test")
        return results

    except Exception as e:
        print(f"❌ {head_info['detect_class']} 测试失败: {{e}}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
'''

    with open(train_file, 'w', encoding='utf-8') as f:
        f.write(train_content)

def main():
    """主函数"""
    print("🔬 YOLOv8 检测头改进方案批量测试")
    print("=" * 50)

    # 创建缺失的配置文件
    print("📝 检查并创建缺失的配置文件...")
    create_missing_configs()

    # 获取检测头配置
    heads_config = get_detection_heads_config()

    # 创建结果目录
    results_dir = create_results_directory()
    print(f"📁 结果将保存到: {results_dir}")

    # 运行所有测试
    all_results = []

    for head_name, head_config in heads_config.items():
        result = run_single_test(head_name, head_config, results_dir)
        all_results.append(result)

        # 短暂休息，避免GPU过热
        time.sleep(10)

    # 生成汇总报告
    report_file = generate_summary_report(all_results, results_dir)

    print("\n🎉 所有测试完成！")
    print(f"📊 查看详细报告: {report_file}")
    print(f"📁 所有结果保存在: {results_dir}")

if __name__ == "__main__":
    main()
