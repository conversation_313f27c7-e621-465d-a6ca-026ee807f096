"""
Efficient Head (高效检测头) 检测头测试脚本

基于 innovation1-c2f-emscp-aaaf 的成功配置
检测头改进: 高效架构 + 参数优化
预期改进: 平衡精度和效率
"""

import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 获取脚本所在目录并切换
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# 添加项目根目录到路径
sys.path.append('../../../')

from ultralytics import YOLO
import torch

def get_fire_smoke_specialized_augmentation():
    """火焰烟雾专用数据增强参数 (基于成功配置)"""
    return {
        # === 火焰烟雾专用颜色增强 ===
        'hsv_h': 0.018,      # 🔥 适度色调变化，保护火焰橙红色
        'hsv_s': 0.75,       # 🔥 较强饱和度变化，增强火焰检测
        'hsv_v': 0.45,       # 💨 较强亮度变化，模拟烟雾密度
        
        # === 火焰动态特征模拟 ===
        'degrees': 12.0,     # 🔥 火焰摆动角度
        'translate': 0.12,   # 🔥 火焰位置变化
        'scale': 0.7,        # 🔥 火焰大小变化
        'shear': 4.0,        # 🔥 火焰形状扭曲
        
        # === 环境条件模拟 ===
        'perspective': 0.0005, # 🏠 建筑物透视效果
        'flipud': 0.0,       # 🔥 火焰向上特性，禁用垂直翻转
        'fliplr': 0.35,      # 🏠 建筑物对称性
        
        # === 复杂场景增强 ===
        'mosaic': 0.75,      # 🏙️ 多场景组合，模拟复杂环境
        'mixup': 0.12,       # 🌫️ 烟雾混合效果
        'copy_paste': 0.08,  # 🔥 火源复制，增加多火点场景
        
        # === 遮挡和干扰模拟 ===
        'erasing': 0.3,      # 🌫️ 烟雾遮挡效果
        'auto_augment': 'randaugment',  # 🎲 自动增强
        
        # === 高级增强策略 ===
        'bgr': 0.0,          # 🎨 保持RGB通道顺序
        'crop_fraction': 1.0, # 📐 完整图像，避免裁剪关键区域
    }

def get_fire_smoke_optimizer_config():
    """火焰烟雾检测优化的训练参数 (Efficient优化)"""
    return {
        # === 学习率策略 (Efficient平衡设计) ===
        'lr0': 0.0015,       # 🎯 标准初始学习率
        'lrf': 0.00015,      # 📉 标准最终学习率
        'momentum': 0.937,   # 🚀 标准动量
        'weight_decay': 0.0007, # ⚖️ 适中正则化
        
        # === 预热策略 (Efficient标准预热) ===
        'warmup_epochs': 4.0,    # 🔥 标准预热期
        'warmup_momentum': 0.8,  # 🌡️ 预热动量
        'warmup_bias_lr': 0.1,   # 📊 预热偏置学习率
        
        # === 学习率调度 ===
        'cos_lr': True,      # 📈 余弦学习率调度
    }

def get_fire_smoke_loss_weights():
    """火焰烟雾检测专用损失权重 (Efficient优化)"""
    return {
        'box': 8.0,          # 📦 标准边界框权重
        'cls': 0.7,          # 🏷️ 标准分类权重
        'dfl': 1.3,          # 📏 标准分布焦点损失权重
    }

def get_fire_smoke_training_strategy():
    """火焰烟雾检测训练策略 (Efficient优化)"""
    return {
        'close_mosaic': 15,  # 🧩 标准关闭马赛克
        'patience': 20,      # 🎯 标准耐心值
        'save_period': 5,    # 💾 定期保存
        'val': True,         # ✅ 启用验证
        'plots': True,       # 📊 生成图表
        'cache': True,       # 💾 缓存数据集
        'amp': True,         # ⚡ 混合精度训练
        'single_cls': False, # 🏷️ 多类别检测
        'rect': False,       # 📐 不使用矩形训练
        'deterministic': True, # 🎲 确定性训练
        'seed': 42,          # 🌱 随机种子
    }

def main():
    try:
        print("🔥 Efficient Head (高效检测头) 测试")
        print("🎯 检测头改进: 高效架构 + 参数优化")
        print("🚀 预期改进: 平衡精度和效率")
        
        # 环境检查
        if not torch.cuda.is_available():
            print("❌ CUDA不可用，请检查GPU环境")
            return None
        
        print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
        print(f"✅ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        
        # 加载模型
        config_path = os.path.join(script_dir, 'config_efficient.yaml')
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
        model = YOLO(config_path)
        
        # 获取专用配置
        aug_params = get_fire_smoke_specialized_augmentation()
        opt_params = get_fire_smoke_optimizer_config()
        loss_params = get_fire_smoke_loss_weights()
        strategy_params = get_fire_smoke_training_strategy()
        
        print("\n🔥 Efficient Head 特性:")
        print("   ⚡ 高效架构: 优化的卷积结构设计")
        print("   🔗 参数共享: 减少冗余参数")
        print("   🚀 计算优化: 针对推理速度优化")
        print("   ⚖️ 平衡设计: 精度和效率的最佳平衡")
        
        # 开始训练
        results = model.train(
            # === 数据集配置 ===
            data='../../../ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
            epochs=20,           # 🕐 快速测试周期
            batch=-1,            # 📦 自动批次大小优化
            imgsz=640,           # 📐 标准分辨率
            
            # === 设备配置 ===
            device=0,            # 🖥️ GPU 0
            workers=8,           # 👥 多进程数据加载
            
            # === 项目配置 ===
            project='../results',
            name='efficient_test',
            exist_ok=True,
            
            # === 优化器配置 ===
            optimizer="AdamW",
            **opt_params,
            
            # === 火焰烟雾专用数据增强 ===
            **aug_params,
            
            # === 专用损失权重 ===
            **loss_params,
            
            # === 训练策略 ===
            **strategy_params,
            
            # === 其他设置 ===
            verbose=True,
            pretrained=True,     # 🎯 使用预训练权重
        )
        
        print("\n🎉 Efficient Head 测试完成！")
        print("📁 结果保存在: ../results/efficient_test")
        print("🎯 Efficient Head 应该提供精度和效率的最佳平衡")
        
        return results
        
    except Exception as e:
        print(f"❌ Efficient Head 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
